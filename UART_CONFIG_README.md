# UART配置命令功能说明

本文档介绍了通过UART接收配置命令来设置BLE从机采样频率的功能实现。

## 功能概述

系统现在支持通过UART接收配置命令，解析后自动配置指定BLE从机的采样频率。当UART接收到符合协议格式的数据帧时，会自动解析并向对应的BLE从机发送配置参数。

## UART协议格式

### 命令帧结构
```
| 帧头1 | 帧头2 | 设备ID(低) | 设备ID(高) | 采样率(低) | 采样率(高) | CRC16(低) | CRC16(高) |
|  0x80 |  0x01 |    0x01    |    0x00    |    0x14    |    0x00    |   CRC_L   |   CRC_H   |
```

### 字段说明
- **帧头**: 固定为 `0x80 0x01`
- **设备ID**: 2字节，小端序，指定要配置的从机设备ID
- **采样频率**: 2字节，小端序，设置的采样频率值（Hz）
- **CRC16**: 2字节，小端序，使用CRC16-CCITT算法计算前6字节的校验值

### 示例命令
配置设备ID为1的从机采样频率为20Hz：
```
80 01 01 00 14 00 [CRC16_L] [CRC16_H]
```

## 代码结构

### 新增文件
1. **uart_config_test.c/h**: 测试程序，用于验证配置命令功能
2. **UART_CONFIG_README.md**: 本说明文档

### 修改文件
1. **ble_master.h**: 添加了配置结构体和函数声明
2. **ble_master.c**: 添加了UART接收回调和配置从机功能
3. **ble_uart.h/c**: 添加了UART接收中断处理功能

## 关键函数

### 1. UART接收回调函数
```c
static void uart_recv_callback(const uint8_t *data, size_t length)
```
- 解析UART接收到的配置命令
- 验证帧头、长度和CRC16校验
- 调用配置从机函数

### 2. 配置从机函数
```c
esp_err_t ble_master_config_slave(uint16_t device_id, uint8_t sample_rate)
```
- 查找指定设备ID的BLE连接
- 向从机的配置特征值写入新的采样频率

### 3. CRC16计算函数
```c
static uint16_t calculate_crc16(const uint8_t *data, size_t length)
```
- 使用CRC16-CCITT算法计算校验值
- 多项式：0x1021，初始值：0xFFFF

## 使用方法

### 1. 初始化
系统启动时会自动初始化UART接收功能：
```c
ble_master_init();  // 会自动设置UART接收回调
```

### 2. 发送配置命令
通过UART发送符合协议格式的配置命令：
```c
// 示例：配置设备1的采样频率为20Hz
uint8_t cmd[] = {0x80, 0x01, 0x01, 0x00, 0x14, 0x00, 0xXX, 0xXX}; // CRC需要计算
uart_write_bytes(UART_NUM_1, cmd, sizeof(cmd));
```

### 3. 测试功能
使用测试程序验证功能：
```c
#include "uart_config_test.h"

// 运行CRC16测试
uart_config_test_crc16();

// 运行配置命令测试
uart_config_test_run();
```

## 配置参数

### UART设置
- **端口**: UART1
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **TX引脚**: GPIO15
- **RX引脚**: GPIO23

### 超时设置
- **接收超时**: 100ms（可配置）
- **帧间隔**: 超过100ms没有新数据则认为当前帧接收完毕

## 错误处理

### 1. 协议错误
- 帧头不匹配：记录警告并丢弃数据
- 帧长度不足：记录警告并丢弃数据
- CRC16校验失败：记录警告并丢弃数据

### 2. BLE错误
- 设备未连接：返回ESP_FAIL
- 特征值未找到：跳过该设备
- 写入失败：记录错误并返回ESP_FAIL

### 3. 日志输出
所有操作都有详细的日志输出，便于调试：
```
I (12345) ESP_MULTI_CONN_CENT: UART received 8 bytes
I (12346) ESP_MULTI_CONN_CENT: Valid UART config command: Device ID=1, Sample Rate=20 Hz
I (12347) ESP_MULTI_CONN_CENT: Successfully configured slave device 1 with sample rate 20 Hz
```

## 注意事项

### 1. 设备ID映射
当前实现中，设备ID与连接句柄的映射关系是简化的（device_id = conn_handle + 1）。在实际应用中，你可能需要：
- 维护一个设备ID到连接句柄的映射表
- 在连接建立时读取从机的设备ID并建立映射
- 或者使用其他方式来识别特定的从机设备

### 2. 并发处理
- UART接收在独立任务中处理，不会阻塞BLE操作
- 配置命令的处理是异步的，通过回调函数确认结果

### 3. 内存管理
- 系统会自动管理UART接收缓冲区
- BLE写入操作使用mbuf，系统会自动释放

## 扩展功能

### 1. 支持更多配置参数
可以扩展配置结构体来支持更多参数：
```c
struct gatts_slave_config {
    uint8_t sample_rate;     // 采样频率
    uint8_t filter_enable;   // 滤波器使能
    uint16_t threshold;      // 阈值设置
};
```

### 2. 支持批量配置
可以扩展协议来支持一次配置多个设备：
```c
// 批量配置帧格式
// 帧头 + 设备数量 + (设备ID + 配置参数) * N + CRC16
```

### 3. 支持配置确认
可以添加配置确认机制：
```c
// 从机配置成功后发送确认帧
// 帧头 + 设备ID + 配置结果 + CRC16
```
