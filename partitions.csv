# Name,   Type, SubType, Offset,  Size, Flags
# Note: if you have increased the bootloader size, make sure to update the offsets to avoid overlap
#
# 8MB Flash 分区表优化方案
# 固件大小约1.3MB，预留增长空间到1.5MB
#
# 分区布局说明：
# 0x0000   - 0x8000   : 二级引导程序 (32KB)
# 0x8000   - 0x9000   : 分区表 (4KB)
# 0x9000   - 0xF000   : NVS (24KB) - 增加到24KB用于WiFi配置等
# 0xF000   - 0x10000  : PHY初始化数据 (4KB)
# 0x10000  - 0x190000 : Factory 应用分区 (1.5MB) - 主应用分区
# 0x190000 - 0x310000 : OTA_0 应用分区 (1.5MB) - OTA更新分区A
# 0x310000 - 0x490000 : OTA_1 应用分区 (1.5MB) - OTA更新分区B
# 0x490000 - 0x492000 : OTA数据分区 (8KB)
# 0x492000 - 0x792000 : SPIFFS存储分区 (3MB) - 调整存储空间
# 0x792000 - 0x800000 : 预留空间 (440KB) - 可用于未来扩展

nvs,      data, nvs,     0x9000,  0x6000,
phy_init, data, phy,     0xf000,  0x1000,
factory,  app,  factory, 0x10000, 0x180000,
ota_0,    app,  ota_0,   0x190000, 0x180000,
ota_1,    app,  ota_1,   0x310000, 0x180000,
otadata,  data, ota,     0x490000, 0x2000,
storage,  data, spiffs,  0x492000, 0x300000,
