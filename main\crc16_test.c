/*
 * CRC16功能测试程序
 * 
 * 用于测试CRC16计算和验证功能
 */

#include "check_crc16.h"
#include "esp_log.h"
#include <string.h>

static const char *TAG = "CRC16_TEST";

/**
 * @brief 测试CRC16计算功能
 */
void crc16_test_calculate(void)
{
    ESP_LOGI(TAG, "Testing CRC16 calculation...");
    
    // 测试数据：80 01 01 00 14 00
    uint8_t test_data[] = {0x80, 0x01, 0x01, 0x00, 0x14, 0x00};
    uint16_t crc = calculate_crc16(test_data, sizeof(test_data));
    
    ESP_LOGI(TAG, "Test data: 80 01 01 00 14 00");
    ESP_LOGI(TAG, "Calculated CRC16: 0x%04X", crc);
    ESP_LOG_BUFFER_HEXDUMP(TAG, test_data, sizeof(test_data), ESP_LOG_INFO);
    
    // 构造完整帧（小端序CRC）
    uint8_t complete_frame[8];
    memcpy(complete_frame, test_data, 6);
    complete_frame[6] = crc & 0xFF;        // CRC低字节
    complete_frame[7] = (crc >> 8) & 0xFF; // CRC高字节
    
    ESP_LOGI(TAG, "Complete frame with CRC (little-endian):");
    ESP_LOG_BUFFER_HEXDUMP(TAG, complete_frame, sizeof(complete_frame), ESP_LOG_INFO);
}

/**
 * @brief 测试CRC16验证功能
 */
void crc16_test_verify(void)
{
    ESP_LOGI(TAG, "Testing CRC16 verification...");
    
    // 测试数据：80 01 01 00 14 00 + CRC16
    uint8_t test_frame[8] = {0x80, 0x01, 0x01, 0x00, 0x14, 0x00, 0x00, 0x00};
    
    // 添加正确的CRC16
    append_crc16(test_frame, 6, 6, true); // 小端序
    
    ESP_LOGI(TAG, "Frame with correct CRC:");
    ESP_LOG_BUFFER_HEXDUMP(TAG, test_frame, sizeof(test_frame), ESP_LOG_INFO);
    
    // 验证CRC16
    bool result = verify_crc16(test_frame, sizeof(test_frame), 6, true);
    ESP_LOGI(TAG, "CRC16 verification result: %s", result ? "PASS" : "FAIL");
    
    // 测试错误的CRC16
    test_frame[6] = 0xFF; // 故意修改CRC
    result = verify_crc16(test_frame, sizeof(test_frame), 6, true);
    ESP_LOGI(TAG, "CRC16 verification with wrong CRC: %s", result ? "PASS" : "FAIL");
}

/**
 * @brief 测试大端序CRC16
 */
void crc16_test_big_endian(void)
{
    ESP_LOGI(TAG, "Testing CRC16 with big-endian...");
    
    // 测试数据：80 01 01 00 14 00 + CRC16
    uint8_t test_frame[8] = {0x80, 0x01, 0x01, 0x00, 0x14, 0x00, 0x00, 0x00};
    
    // 添加大端序CRC16
    append_crc16(test_frame, 6, 6, false); // 大端序
    
    ESP_LOGI(TAG, "Frame with big-endian CRC:");
    ESP_LOG_BUFFER_HEXDUMP(TAG, test_frame, sizeof(test_frame), ESP_LOG_INFO);
    
    // 验证大端序CRC16
    bool result = verify_crc16(test_frame, sizeof(test_frame), 6, false);
    ESP_LOGI(TAG, "Big-endian CRC16 verification: %s", result ? "PASS" : "FAIL");
}

/**
 * @brief 运行所有CRC16测试
 */
void crc16_test_run_all(void)
{
    ESP_LOGI(TAG, "=== Starting CRC16 Tests ===");
    
    crc16_test_calculate();
    ESP_LOGI(TAG, "");
    
    crc16_test_verify();
    ESP_LOGI(TAG, "");
    
    crc16_test_big_endian();
    
    ESP_LOGI(TAG, "=== CRC16 Tests Completed ===");
}

/**
 * @brief 测试边界条件
 */
void crc16_test_edge_cases(void)
{
    ESP_LOGI(TAG, "Testing edge cases...");
    
    // 测试空数据
    uint16_t crc_empty = calculate_crc16(NULL, 0);
    ESP_LOGI(TAG, "CRC16 for NULL/empty data: 0x%04X", crc_empty);
    
    // 测试单字节数据
    uint8_t single_byte = 0xAA;
    uint16_t crc_single = calculate_crc16(&single_byte, 1);
    ESP_LOGI(TAG, "CRC16 for single byte 0xAA: 0x%04X", crc_single);
    
    // 测试验证函数的边界条件
    uint8_t short_frame[1] = {0x80};
    bool result = verify_crc16(short_frame, 1, 0, true);
    ESP_LOGI(TAG, "Verify short frame (should fail): %s", result ? "PASS" : "FAIL");
    
    result = verify_crc16(NULL, 8, 6, true);
    ESP_LOGI(TAG, "Verify NULL data (should fail): %s", result ? "PASS" : "FAIL");
}
